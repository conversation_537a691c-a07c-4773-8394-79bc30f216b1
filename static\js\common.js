﻿
$(function () {
    // meun
    $('.meun').click(function () {
        $(this).toggleClass('on');
        $('.header').toggleClass('on');
    })

    
	//跳转评论 
	 $('.detaile_top .pl,.dj_news_zl .ZL_M1 .pl').click(function(){
        var cms_top = $('.cms_wrap').offset().top;
        $("body,html").animate({scrollTop:cms_top},300);
    })

    //搜索
    $('.hotsearch').css('height',document.body.scrollHeight)
    $('.search_input').focus(function () {
        $('body').css({"overflow":"initial",})
        $('.hotsearch').slideDown()
    })
    $('.search_input').blur(function () {
        $('body').css({"overflow":"initial"})
        $('.hotsearch').slideUp();
    })


  $(function () {
      //sy-meun
      var num=$(".box_c .nav_c ul li").length;
      var l=$(window).width(); //屏幕宽度
      var j=0;
      var p=null;
      var b=null;

       b =$(".box_c .nav_c ul li").eq(num-1).width()+30; //最后一个a的长度
      for(var i=0;i<num;i++){
          j=j+$(".box_c .nav_c ul li").eq(i).width()+31;
          // j=j+$(".box_c .nav_c ul li").eq(i).width()+30;
      };
      $(".box_c .nav_c ul").css("width",j+"px");//ul的长度
      var obj= $(".box_c .nav_c ul li .on");
      if(obj.html()){p=obj.offset().left;}
      if(j>l){
          if(p<=j&&p>l-90){
              var e=p-l/2;
              var f=l/2-b;
              var z=j-l+12;
              if(e<f){
                  L2=l/2;
                  $(".box_c .nav_c").scrollLeft(L2);
              }else{
                  $(".box_c .nav_c").scrollLeft(z);
              }
          }
      }
  })

    //PAGE
    $(".page>span .cbtn").css("display","none");
    $(".page>span").click(function(){
        $(this).children("div").show();
    })
    $(document).click(function(){$(".page>span .cbtn").css("display","none");})
    $(".page>span .cbtn,.page>span").click(function(event){
        event.stopPropagation();
    });
    // UP
    $('footer .bot .up').click(function () {
        $("body,html").animate({scrollTop:0},300);
    })


})


$(function(){
	    //游戏介绍
    if($('.Gm_jieshao').size()>=1){
        var txt_h =$('.Gm_jieshao >p').height();
        var txt_h1=$('.Gm_jieshao').height();
		var top_=$(".Gm_jieshao ").offset().top;
        if(txt_h1>txt_h){
            $('.Gm_jieshao').addClass('show_js');
            $('.Gm_jieshao').css({height:txt_h+"px","overflow":"hidden"})
            $('.Gm_jieshao .morbtn').show();
            $('.Gm_jieshao .morbtn').click(function () {
                if($('.Gm_jieshao').hasClass('show_js')){
                    $('.Gm_jieshao').removeClass('show_js');
                    $('.Gm_jieshao .morbtn span').html('收起更多');
                    $('.Gm_jieshao').css({height:"auto"});
                }else{
                    $('.Gm_jieshao').addClass('show_js');
                    $('.Gm_jieshao .morbtn span').html('展开更多');
                    $('.Gm_jieshao').css({height:txt_h+"px"});
					 $("body,html").animate({scrollTop:top_-40+"px"},0);
                }
            })
        }
    }
})


//文字提示弹窗 自动关闭
window.tipsmsg = function (txt) {
    if($('.tips_wind').size()<=0){
        $('body').append('<div class="tips_wind"><span>'+txt+'</span></div>')
        $('.tips_wind').fadeIn()
        setTimeout(function () {
            $('.tips_wind').fadeOut(function () {
                $('.tips_wind').remove()
            });
        },1000)
    }
}


//文字提示 等待关闭
window.tipsmsg2 = function (txt) {
    if($('.tips_wind2').size()<=0){
        $('body').append('<div class="tips_wind2"><div class="bg"></div><span> <i>'+txt+' </i> <a href="javascript:void(0)" class="clos">确认</a> </span> </div>')
        $('.tips_wind2').fadeIn()
        $('.tips_wind2,.tips_wind2 .bg').click(function () {
            $('.tips_wind2').fadeOut(function () {
                $('.tips_wind2').remove();
            })
        })
    }
}

//video

$(function () {var wind_w = $(window).width();if(wind_w<=768){$('.detaile_cont p iframe,.dj_news_zl .ZL_M3 p iframe,.Gm_jieshao p iframe').css('height',wind_w*0.6)}})

//查看很多
if( $(".moreshowbox ul li").size()>=16){
	 $(".moreshowbox ul li:gt(15)").hide();
	 $(".moreshowbox .morebtn").show();
	 $(".moreshowbox .morebtn p").click(function(){
		 $(".moreshowbox ul li").show();
		 $(this).parents(".morebtn").hide()
	 })
}




//
// /*收藏*/
// $('.ction_btn').click(function () {
//     if($(this).find('i').hasClass('on')){
//         $(this).html('<i></i>收藏');
//         tipsmsg('已取消')
//     }else{
//         $(this).html('<i class="on"></i>已收藏');
//         tipsmsg('已收藏')
//     }
// })
//
// //回复
// $('.reply_btn').click(function () {
//     var replayid = $(this).attr('data-replayid');
//     if($(this).hasClass('on')){
//         $(this).removeClass('on')
//         $(this).html('回复')
//         $('.cmt_reply_box[data-replaybox='+replayid+']').stop().slideUp();
//     }else{
//         $(this).addClass('on')
//         $(this).html('取消回复')
//         $('.cmt_reply_box[data-replaybox='+replayid+']').slideDown();
//     }
//
//
// })
//
// //评论点赞
// $('.cmt_score div').click(function () {
//     var a = $(this).parents('.cmt_score')
//     if( a.find('.on').size()>=1){
//         tipsmsg('此评论已点赞')
//     }else{
//         $(this).addClass('on')
//         var numr = Number($(this).html());
//         $(this).html(numr+1);
//     }
// })
//
// $('.cmt_floor').each(function(){
//     var lis = $(this).find('.lis').size();
//     //console.log(lis)
//     if(lis>3){
//         var this_ = $(this).find('.lis');
//         this_.hide();
//         this_.eq(0).show();
//         this_.eq(1).show();
//         this_.eq(lis-1).show();
//         this_.eq(1).after('<div class="floor_tips"><p>楼层太高已隐藏'+Number(lis-3)+'条</p></div>');
//         $('.cmt_floor .floor_tips').on('click',function(){
//             $(this).siblings('.lis').show();
//             $(this).remove();
//         })
//     }
// })

 