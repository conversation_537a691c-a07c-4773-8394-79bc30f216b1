﻿function getdownnums(id){

    return false;
}

function openyuye(){
    $('.yymengban ,.yywrap').fadeIn();
    $('.yywrap').css('left',($(window).width() - $('.yywrap').width())/2 );
    $('.yywrap').css('top',($(window).height() - $('.yywrap').height())/2 );
}

function closeyuyue(){
    $('.yymengban ,.yywrap').fadeOut();
}
//下载统计
var xiazai_url = "";
var xiazai_title = "";
var xiazai_img = "";
var xiazai_down = "";
function downcount(type,id) {
    if ( type == "" || id <= 0 ){
        return false;
    }
    $.post("/api/countdown", {type: type, id: id}, function(){
        return false;
    });
    if(type == "zhushou" && typeof gotoZhushou === "function"){
        gotoZhushou(id);
    }
    if(type == "zhushou" && $(".xiazai_zhushou").length > 0){
        if(xiazai_url == ""){
            xiazai_url = $(".xiazai_zhushou").attr('href');
            $(".xiazai_zhushou").attr('href', 'javascript:void(0);');
            xiazai_title = $('.xiazai_title').html();
            xiazai_img = $(".xiazai_img img").attr('src');
            xiazai_down = $(".down_list_a .btn1").attr('href');
            xiazai_down = xiazai_down.replace('/1_','/14_');
        }
        if(xiazai_url == ""){
            return true;
        }
        var appos = getappos();
        if(appos == 0){
            window.location.href = xiazai_url;
            return true;
        }
        if(isInWx()){
            window.location.href = xiazai_url;
            return true;
        }else{      
            //非微信中
        if(appos == 2){　
            window.location.href = xiazai_url;　　　
            return true;
        }else{      
                //android系统，通过定时器的方式，判断是否安装有APP
                var hasApp = true , t = 1000;
                setTimeout(function () {  
                    //没有安装APP则直接下载手机助手，延时时间设置为2秒
                    if(!hasApp) {
                        window.location.href = xiazai_url;
                        return true;
                    }
                } , 2000);
                var t1 = Date.now();
                try{
                    setTimeout(function () {
                    //t的时间就是出发APP启动的时间，若APP启动了，再次返回页面时t2这行代码执行，hasApp即为true。反之若APP没有启动即为false
                    var t2 = Date.now();
                        hasApp = !(!t1 || t2 - t1 < t + 150);
                    } , t);
                    window.location.href = "launchapp://3dmgame?title="+encodeURIComponent(xiazai_title)+"&url="+encodeURIComponent(xiazai_down)+"&img="+encodeURIComponent(xiazai_img);
                }catch(err){

                }                
            }
        }
    }
}

function yuyueform(id){

    var myreg = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1})|(17[0-9]{1}))+\d{8})$/;
    var mobile = $('.phone').val();

    if ( mobile == "" ){
        $('.yy_form .tips p').html('*请输入手机号')
        return false;
    }
    else if ( !myreg.test(mobile) ){
        $('.yy_form .tips p').html('*请输入正确手机号码')
        return false;
    }

    $('.yy_form .tips p').html('');
    $.post('/api/appointsoft', {mobile: mobile, softid: id}, function(data){
        if ( data.code == 1 ){
            $('.yy_form .tips p').html('预约成功！');
        }
        else{
            $('.yy_form .tips p').html(data.msg);
        }

        return false;

    },'json');
}

function getappos()
{
    var os = 0;
    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        os = 2;
    } else if (/(Android)/i.test(navigator.userAgent)) {
        os = 1;
    }
    return os;
}

function isInWx(){
    return window.navigator.userAgent.toLowerCase().match(/microMessenger/i)=='micromessenger' ? true : false;
}
