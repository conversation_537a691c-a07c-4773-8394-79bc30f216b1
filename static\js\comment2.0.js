﻿$(function () {
    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        $(".agent_ioscss").removeClass('agent_dishdcss');
        $(".agent_androidcss").addClass('agent_dishdcss');
    }
    //首页的轮播
    if ($(".Min_swiper .swiper-container .swiper-slide").size() >= 1) {
        var Min_swiper = new Swiper('.Min_swiper .swiper-container', {
            lazy: true,
            speed: 200,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.Min_swiper .swiper-pagination',
                clickable: true,
            }
        });
    }
    //首页颜色标签
    $('.Color_label li').each(function () {
        $(this).attr('class',`cl_${Math.round(Math.random()*3)}`)
    })
    //导航头部
    if ($(".Min_nav .swiper-container .swiper-slide").size() >= 1) {
        var side = 5;
        var swiper_nav = new Swiper('.Min_nav .swiper-container', {
            slidesPerView: side,
        });
        $(".Min_nav .swiper-container .swiper-slide").each(function () {
            if ($(this).hasClass("on")) {
                var index_ = $(this).index();
                swiper_nav.slideTo(index_, false);//切换到第一个slide，速度为1秒;
            }
        })
    }
    //游戏评测
    if ($(".Min2 .swiper-container .swiper-slide").size() >= 1) {
        var Min2_swiper = new Swiper('.Min2 .swiper-container', {
            lazy: true,
            speed: 200,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.Min2 .swiper-pagination',
                clickable: true,
            },

        });
    }
    //新游测试
    if ($(".Min3 .swiper-container .swiper-slide").size() >= 1) {
        var Min3_swiper = new Swiper('.Min3 .swiper-container', {
            effect: 'coverflow',
            grabCursor: true,
            initialSlide: 1,
            loop: true,
            slidesPerView: "auto",
            observer: true,
            centeredSlides: true,
            autoplay: true,
            coverflowEffect: {
                rotate: 0,
                stretch: 73,
                depth: 300,
                modifier: 1,
                slideShadows: true
            }
        });
    }

    //网游活动
    if ($(".Min4 .swiper-container .swiper-slide").size() >= 1) {
        var Min4_swiper = new Swiper('.Min4 .swiper-container', {
            slidesPerView: 3,
        });
    }
    //首页切换
    $('.tab_wrap .tabhd span').click(function () {
        $(this).addClass('on').siblings().removeClass('on')
        $('.tab_wrap .tabbd .list_tj').hide().eq($(this).index()).show();
    })
    // 问答切换
    $('.tab_hd .item').click(function () {
        $(this).addClass('on').siblings().removeClass('on')
        $('.tab_bd ul').hide().eq($(this).index()).show();
    })
    //标签
    if ($(".swiper_nav_ .swiper-container .swiper-slide").size() >= 1) {
        var swiper_nav_ = new Swiper('.swiper_nav_ .swiper-container', {
            slidesPerView: 4,
        });
        $(".swiper_nav_ .swiper-container .swiper-slide").each(function () {
            if ($(this).hasClass("on")) {
                var index_ = $(this).index();
                swiper_nav_.slideTo(index_, false);//切换到第一个slide，速度为1秒;
            }
        })
    }
    //落地页截图
    if ($('#Gm-pic').size() >= 1) {
        var _test = $('#Gm-pic .swiper-slide').eq(0).find('img');
        var imgObj = new Image();
        imgObj.src = _test.attr("src");

        imgObj.onload = function () {
            var img_h = imgObj.height;
            var img_w = imgObj.width;
            if (img_h > 0) {
                if (img_h > img_w) {
                    new Swiper('#Gm-pic', {
                        //loop: true,
                        slidesPerView: 2,
                        autoHeight: true,
                        spaceBetween: 4,
                        // zoom : true,
                    })
                } else {
                    new Swiper('#Gm-pic', {
                        // loop: true,
                        autoHeight: true,
                    })
                }
            }
        }
    }
    //展开更多版本
    if($('.bblist a').length>4){$('.open_more').show()}
    $('.open_more').click(function(){
        if($(this).html()=='展开更多版本'){
            $(this).html('收起更多版本')
            $('.bblist').css('max-height','inherit')
        }else{
            $(this).html('展开更多版本')
            $('.bblist').css('max-height','35px')
        }
    })
	
	 //展开更多文章内容
	 if ($('.Gm-desc').size() >= 10) {
        // var txt_h = $('.Gm-desc >p').height();
        var txt_h = 600;
        var txt_h1 = $('.Gm-desc').height();
        var top_ = $(".Gm-desc ").offset().top;
        if (txt_h1 > txt_h) {
            $('.Gm-desc').addClass('show_js2');
            $('.Gm-desc').css({height: txt_h + "px", "overflow": "hidden", "padding-bottom": "45px"})
            $('.Gm-desc .morbtn').show();
            $('.Gm-desc .morbtn').click(function () {
                if ($('.Gm-desc').hasClass('show_js2')) {
                    $('.Gm-desc').removeClass('show_js2');
                    $('.Gm-desc .morbtn span').html('收起更多');
                    $('.Gm-desc').css({height: "auto"});
                } else {
                    $('.Gm-desc').addClass('show_js2');
                    $('.Gm-desc .morbtn span').html('展开更多');
                    $('.Gm-desc').css({height: txt_h + "px"});
                    $("body,html").animate({scrollTop: top_ - 40 + "px"}, 0);
                }
            })
        }
    }
    
    //显示切换豌豆荚
   function wdjshow(){
			if( $(".downl_head .btnwrap p span").hasClass("Chioce")){
				$(".downl_head .btnwrap .downl_wrap2 .wdj1").hide()
			}
			$(".downl_head .btnwrap .wdj_Chioce").click(function () {
			   if( $(this).find("span").hasClass("Chioce")){
				   $(".downl_head .btnwrap .downl_wrap2 .wdj1").show();
				   $(".downl_head .btnwrap .downl_wrap2 .wdj_btn").hide();
				   $(this).find("span").removeClass("Chioce")
			   }else{
				   $(".downl_head .btnwrap .downl_wrap2 .wdj1").hide();
				   $(".downl_head .btnwrap .downl_wrap2 .wdj_btn").show();
				   $(this).find("span").addClass("Chioce")

			   }
			})
		}
		wdjshow()

    //无广告则豌豆荚检测包名
    var wdjdownurl = $(".wdj1").length > 0 ? $(".wdj1").attr("href") : '';
    wdjdownurl = '';
    if($('#wdjkeyname').length > 0 && (wdjdownurl.indexOf('/1_') > 0 || wdjdownurl.indexOf('.apk') > 0)){
        var ptype =  $('#soft_ptype').val();
        if(ptype == 1 || ptype == 2){
            // $('.wdj_Chioce').html('<span class="Chioce"></span>使用3DM游戏官方渠道下裁目标软件，专享加速下载通道');
            // $(".wdj_btn").attr('href', 'javascript:void(0);');
        } else if(ptype == 3){
        }
    }else{
        $('.Chioce').click();
        $('.wdj_Chioce').hide();
    }
});

//游戏列表导航头部
if ($(".Mingame .Smingam1 .swiper-slide").size() >= 1) {
    var side = 3.6;
    var swiper_nav = new Swiper('.Smingam1 ', {
        slidesPerView: side,
        observer: true,  //开启动态检查器，监测swiper和slide
        observeParents: true,  //监测Swiper 的祖/父元素
    });
}
//游戏列表轮播
if ($(".Mingame .lunbox .swiper-slide").size() >= 1) {
    var Min_swiper = new Swiper('.lunbox .swiper-container', {
        lazy: true,
        speed: 200,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.lunbox .swiper-pagination',
            clickable: true,
        }
    });
}

function gotoZhushou(id){
    if($('#wdjkeyname').length > 0){
        //九游
        /*var ptype = $('#soft_ptype').val();
        var url = "";
        if($(".wdj1").length > 0){
            url = window.location.href;
        }
        $.ajax({
            url:'/api/getjiuy',
            data:{type:ptype, url:url},
            type:"POST",
            dataType:"json",
            success:function (res) {
                if(res.code == 1){
                    window.open(res.arcurl);
                }else{
                    $('.Chioce').click();
                    $('.wdj_Chioce').hide();
                }
            },
            error:function(err){
                $('.Chioce').click();
                $('.wdj_Chioce').hide();
            }
        });*/

        //应用宝
        var ptype = $('#soft_ptype').val();
        var url = "";
        if($(".wdj1").length > 0){
            url = $(".wdj1").attr("href");
        }
        $.ajax({
            url:'/api/getyingyonbao',
            data:{id:id,type:ptype, title:$('#wdjkeyname').val(), url:url},
            type:"POST",
            dataType:"json",
            success:function (res) {
                if(res.code == 1){
                    window.open(res.downurl);
                }else{
                    $('.Chioce').click();
                    $('.wdj_Chioce').hide();
                }
            },
            error:function(err){
                $('.Chioce').click();
                $('.wdj_Chioce').hide();
            }
        });
    }else{
        $('.Chioce').click();
        $('.wdj_Chioce').hide();
    }
    
}

 /* 23-11-29  迭代 */
if ($(".swiperauto .swiper-container .swiper-slide").size() >= 1) {
  var swiperauto = new Swiper('.swiperauto .swiper-container', {
	  slidesPerView: "auto",
	  observer: true,
	  observeParents: true,
	  spaceBetween: 0,
	  on: {
		  touchEnd: function (swiper, event) {
			  $(window).trigger('scroll');
		  },
	  },
  });
}
// 切换公共
$(".switchbox  .switchlab  .a").click(function () {
    $(this).addClass("on").siblings().removeClass("on");
    $(this).parents(".switchbox").find(".switchitem .item_").eq($(this).index()).show().siblings().hide();
    $(this).parents(".switchbox").find(".switchmore a").eq($(this).index()).show().siblings().hide();
    $(window).trigger('scroll');
}) 


